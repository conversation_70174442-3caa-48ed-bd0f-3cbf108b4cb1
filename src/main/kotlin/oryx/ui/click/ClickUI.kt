package oryx.ui.click

import net.minecraft.client.gui.GuiScreen
import org.jetbrains.skia.FilterBlurMode
import org.jetbrains.skia.ImageFilter
import org.jetbrains.skia.MaskFilter
import org.jetbrains.skia.Paint
import oryx.utility.math.Vec2
import oryx.utility.skia.mouseOver
import oryx.utility.skia.render
import oryx.utility.skia.roundRect
import java.awt.Color

object ClickUI : GuiScreen() {
    private val pos = Vec2()
    private val size = Vec2(400F, 220F)
    private val drag = Vec2()
    private var dragging = false

    private val bgGlow = Paint().apply {
        color = Color(0, 0, 0, 120).rgb
        imageFilter = ImageFilter.makeDropShadow(0f, 0f, 10f, 10f, color)
        isAntiAlias = true
    }

    private val bgPaint = Paint().apply {
        color = Color(0, 0, 0, 120).rgb
        maskFilter = MaskFilter.(FilterBlurMode.NORMAL, 10f)
        imageFilter = ImageFilter.makeDropShadow(0f, 0f, 10f, 10f, color)
        isAntiAlias = true
    }

    fun draw() {


        render {
            roundRect(pos.x, pos.y, size.x, size.y, 5F, bgPaint)
        }
    }

    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        if (dragging) {
            pos.x = mouseX + drag.x
            pos.y = mouseY + drag.y
        }
    }

    override fun mouseClicked(mouseX: Int, mouseY: Int, mouseButton: Int) {
        if (
            mouseOver(pos.x, pos.y, size.x, size.y, mouseX.toFloat(), mouseY.toFloat()) &&
            mouseButton == 0
        ) {
            dragging = true
            drag.x = pos.x - mouseX
            drag.y = pos.y - mouseY
        }
    }

    override fun mouseReleased(mouseX: Int, mouseY: Int, state: Int) {
        dragging = false
    }
}