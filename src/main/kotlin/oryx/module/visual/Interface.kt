package oryx.module.visual

import org.jetbrains.skia.*
import oryx.Oryx
import oryx.event.base.Listen
import oryx.event.impl.Render2DEvent
import oryx.module.base.Module
import oryx.module.base.ModuleManager
import oryx.utility.skia.canvas
import oryx.utility.skia.font.Fonts
import oryx.utility.skia.font.Style
import oryx.utility.skia.render
import java.awt.Color

object Interface : Module() {
    private val watermarkFont = Fonts.NUNITO[Style.BOLD, 18F]
    private val otherFont = Fonts.NUNITO[Style.REGULAR, 8F]
    private val metrics = otherFont.metrics

    private val textPaint = Paint().apply {
        color = Color(160, 20, 240, 240).rgb
        isAntiAlias = true
    }

    private val bgPaint = Paint().apply {
        color = Color(0, 0, 0, 120).rgb
        imageFilter = ImageFilter.makeDropShadow(0f, 0f, 10f, 10f, textPaint.color)
        pathEffect = PathEffect.makeCorner(5f)
        isAntiAlias = true
    }

    init {
        enabled = true
    }

    @Listen
    private fun onRender(event: Render2DEvent) {
        render {
            watermarkFont.text(Oryx.NAME, 10F, 20F, Color(255, 255, 255, 255))

            val modules = ModuleManager.filter { it.enabled }.sortedByDescending { it.name.length }

            val padding = 3f
            var currentY = 35f
            val lineSpacing = 0f
            val startX = 10f

            val lineHeight = metrics.descent - metrics.ascent

            var backgroundPath = Path()
            backgroundPath.use {
                for (text in modules) {
                    val textWidth = otherFont.width(text.name)

                    val lineBounds = Rect.makeLTRB(
                        startX - padding,
                        currentY - padding,
                        startX + textWidth + padding,
                        currentY + lineHeight
                    )

                    backgroundPath = Path.makeCombining(backgroundPath, Path().addRect(lineBounds), PathOp.UNION) ?: Path()

                    currentY += lineHeight + lineSpacing
                }

                canvas.drawPath(backgroundPath, bgPaint)

                currentY = 39f

                for (text in modules) {
                    otherFont.text(text.name, startX, currentY + metrics.descent, textPaint)
                    currentY += lineHeight + lineSpacing
                }
            }
        }
    }
}