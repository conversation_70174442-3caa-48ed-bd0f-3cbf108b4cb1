package oryx.module.visual

import org.lwjgl.input.Keyboard
import oryx.event.base.Listen
import oryx.event.impl.Render2DEvent
import oryx.module.base.Module
import oryx.ui.click.ClickUI
import oryx.utility.misc.Timer
import oryx.utility.misc.mc

object ClickGUI : Module() {
    init {
        key = Keyboard.KEY_RSHIFT
    }

    override fun enable() {
        mc.displayGuiScreen(ClickUI)
    }

    @Listen
    private fun onRender(event: Render2DEvent) {
        ClickUI.draw()
    }
}