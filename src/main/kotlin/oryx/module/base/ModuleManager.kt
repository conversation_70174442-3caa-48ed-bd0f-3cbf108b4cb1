package oryx.module.base

import oryx.event.base.Listen
import oryx.event.base.Listenable
import oryx.event.impl.KeyEvent
import oryx.module.combat.KillAura
import oryx.module.move.NoSlow
import oryx.module.move.Sprint
import oryx.module.visual.ClickGUI
import oryx.module.visual.Interface
import oryx.utility.misc.Manager

object ModuleManager : Manager<Module>(), Listenable {

    init {
        add(
            Sprint,
            KillAura,

            NoSlow,

            ClickGUI,
            Interface
        )
        register()
    }

    @Listen
    private fun onKey(event: KeyEvent) {
        filter { it.key == event.key }.forEach { it.toggle() }
    }

    operator fun get(name: String) = find { it.name.equals(name, true) }
}