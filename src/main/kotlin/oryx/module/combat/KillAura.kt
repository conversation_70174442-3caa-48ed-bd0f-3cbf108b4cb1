package oryx.module.combat

import net.minecraft.entity.EntityLivingBase
import org.lwjgl.input.Keyboard
import oryx.event.base.Listen
import oryx.event.impl.UpdateEvent
import oryx.module.base.Module
import oryx.utility.misc.mc
import oryx.utility.rotation.RotationManager
import oryx.utility.rotation.closetRotation
import oryx.utility.rotation.getEntityHitVec

object KillAura : Module() {
    init {
        key = Keyboard.KEY_R
    }

    @Listen
    fun onUpdate(event: UpdateEvent) {
        val targets = mc.world.loadedEntityList
        targets.sortBy { mc.player.getDistanceToEntity(it) }
        val target = targets.firstOrNull { it != mc.player && it is EntityLivingBase }

        if (target != null &&
            target.isEntityAlive &&
            mc.player.canEntityBeSeen(target) &&
            mc.player.getDistanceToEntity(target) <= 8
        ) {
            val rotation = closetRotation(target)
            RotationManager.rotateTo(rotation, true)

            if (mc.player.getDistanceToEntity(target) <= 5 && target.hurtResistantTime <= 15) {
                //mc.player.swingItem()
                //mc.playerController.attackEntity(mc.player, target)
                mc.clickMouse()
            }
        }
    }
}