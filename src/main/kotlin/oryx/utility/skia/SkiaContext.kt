@file:JvmName("SkiaContext")

package oryx.utility.skia

import net.minecraft.client.gui.ScaledResolution
import net.minecraft.client.renderer.GlStateManager
import org.jetbrains.skia.*
import org.lwjgl.opengl.GL11.*
import oryx.utility.misc.mc
import oryx.utility.skia.gl.States
import java.util.function.Consumer

private val context = DirectContext.makeGL()
private lateinit var surface: Surface
private lateinit var renderTarget: BackendRenderTarget
private val scaledResolution
    get() = ScaledResolution(mc)

val canvas
    get() = surface.canvas

val scaleFactor: Int
    get() = scaledResolution.scaleFactor

fun createSurface(width: Int = mc.displayWidth, height: Int = mc.displayHeight) {
    val fbo = mc.framebuffer.framebufferObject
    renderTarget = BackendRenderTarget.makeGL(
        width, height,
        0, 8,
        fbo, GL_RGBA8
    )

    surface = Surface.makeFromBackendRenderTarget(
        context, renderTarget,
        SurfaceOrigin.BOTTOM_LEFT,
        SurfaceColorFormat.RGBA_8888,
        ColorSpace.sRGB
    ) ?: throw IllegalStateException("Failed to create surface")
}

fun preFlush() {
    States.backup()
    GlStateManager.clearColor(0F, 0F, 0F, 0F)
    context.resetGLAll()
    glDisable(GL_ALPHA_TEST)
}

fun flush() {
    context.flush()
    States.restore()
}

fun render(drawLogic: Consumer<Canvas>) {
    preFlush()

    canvas.save()
    val scale = scaleFactor.toFloat()
    canvas.scale(scale, scale)

    try {
        drawLogic.accept(canvas)
    } finally {
        canvas.restore()
    }

    flush()
}

fun onResize(width: Int, height: Int) {
    surface.close()
    renderTarget.close()
    createSurface(width, height)
}