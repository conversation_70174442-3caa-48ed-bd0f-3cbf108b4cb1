package oryx.utility.skia.font

import oryx.utility.file.loadFont

enum class Fonts(val fontName: String, val extension: String = ".ttf") {
    NUNITO("Nunito-%s");

    private val cache = HashMap<Float, SkiaFont>()

    operator fun get(
        style: Style = Style.REGULAR,
        size: Float
    ) = cache.computeIfAbsent(size) {
        val fontPath = String.format(fontName, style.name)
        val font = loadFont("$fontPath$extension", size)

        SkiaFont(font)
    }
}