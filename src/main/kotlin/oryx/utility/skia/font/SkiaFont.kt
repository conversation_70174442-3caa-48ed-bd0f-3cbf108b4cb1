package oryx.utility.skia.font

import org.jetbrains.skia.*
import oryx.utility.skia.canvas
import oryx.utility.skia.paint
import java.awt.Color

class SkiaFont(val font: Font) {
    val metrics
        get() = font.metrics

    fun text(text: String, x: Float, y: Float, color: Color) =
        canvas.drawString(text, x, y, font, paint(color))

    fun text(text: String, x: Float, y: Float, paint: Paint) =
        canvas.drawString(text, x, y, font, paint)

    fun centeredText(text: String, x: Float, y: Float, color: Color) = text(text, x - (width(text) / 2), y, color)

    fun measure(text: String) = font.measureText(text)

    fun height(text: String) = measure(text).height

    fun width(text: String) = measure(text).width

    fun close() = font.close()

    private val lines = listOf(
        "Test 123456789",
        "Test 1234567",
        "Test 12345",
        "Test 123",
        "Test"
    )

    private val textPaint = Paint().apply {
        color = Color(255, 100, 255, 200).rgb
        isAntiAlias = true
    }

    private val bgPaint = Paint().apply {
        color = Color(255, 20, 20, 200).rgb
        isAntiAlias = true
        //mode = PaintMode.FILL
        pathEffect = PathEffect.makeCorner(5f)
    }

    fun onPaint() {
        val padding = 5f
        var currentY = 35f
        val lineSpacing = 0f
        val startX = 10f

        val lineHeight = metrics.descent - metrics.ascent

        var backgroundPath = Path()
        backgroundPath.use {
            for (text in lines) {
                val textWidth = width(text)

                val lineBounds = Rect.makeLTRB(
                    startX - padding,
                    currentY - padding,
                    startX + textWidth + padding,
                    currentY + lineHeight
                )

                backgroundPath = Path.makeCombining(backgroundPath, Path().addRect(lineBounds), PathOp.UNION) ?: Path()

                currentY += lineHeight + lineSpacing
            }

            canvas.drawPath(backgroundPath, bgPaint)

            currentY = 40f

            for (text in lines) {
                canvas.drawString(text, startX, currentY + metrics.descent, font, textPaint)
                currentY += lineHeight + lineSpacing
            }
        }
    }
}