package oryx.utility.extension

import org.jetbrains.skia.Path
import org.jetbrains.skia.PathOp
import kotlin.math.PI

fun Double.toRadians() = this * PI / 180
fun Float.toRadians() = this * PI / 180
fun Int.toRadians() = this * PI / 180
fun Long.toRadians() = this * PI / 180

fun Double.toDegrees() = this * 180 / PI
fun Float.toDegrees() = this * 180 / PI
fun Int.toDegrees() = this * 180 / PI
fun Long.toDegrees() = this * 180 / PI

fun Boolean.toInt() = if (this) 1 else 0

fun Path.op(other: Path, op: PathOp) = Path.makeCombining(this, other, op) ?: Path()
