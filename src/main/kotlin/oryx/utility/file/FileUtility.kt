package oryx.utility.file

import net.minecraft.util.ResourceLocation
import org.jetbrains.skia.Data
import org.jetbrains.skia.Font
import org.jetbrains.skia.FontMgr
import oryx.utility.misc.mc
import java.io.BufferedReader
import java.io.InputStreamReader

private val resourceManager = mc.resourceManager

fun loadFont(name: String, size: Float): Font {
    val location = ResourceLocation("font/$name")
    return resourceManager
        .getResource(location)
        .inputStream
        .use { stream ->
            Data.makeFromBytes(stream.readBytes())
        }
        .let { data ->
            val face = FontMgr.default.makeFromData(data)
            data.close()
            Font(face, size)
        }
}

fun loadShader(name: String): String {
    val location = ResourceLocation("shaders/other/$name")
    val inputStream = resourceManager.getResource(location).inputStream
    val sourceBuilder = StringBuilder()

    inputStream.use {
        val inputStreamReader = InputStreamReader(inputStream)
        val bufferedReader = BufferedReader(inputStreamReader)
        var line: String?
        while (bufferedReader.readLine().also { line = it } != null) {
            sourceBuilder.append(line).append('\n')
        }
        return sourceBuilder.toString()
    }
}