package oryx.utility.render.animation

enum class Ease(val easing: (Float) -> Float) {
    LINEAR({ it }),
    IN_QUAD({ it * it }),
    OUT_QUAD({ 1 - (1 - it) * (1 - it) }),
    IN_OUT_QUAD({ if (it < 0.5f) 2 * it * it else 1 - (-2 * it + 2) * (-2 * it + 2) / 2 }),
    IN_CUBIC({ it * it * it }),
    OUT_CUBIC({ 1 - (1 - it) * (1 - it) * (1 - it) }),
    IN_OUT_CUBIC({ if (it < 0.5f) 4 * it * it * it else 1 - (-2 * it + 2) * (-2 * it + 2) * (-2 * it + 2) / 2 }),
    IN_QUART({ it * it * it * it }),
    OUT_QUART({ 1 - (1 - it) * (1 - it) * (1 - it) * (1 - it) }),
    IN_OUT_QUART({ if (it < 0.5f) 8 * it * it * it * it else 1 - (-2 * it + 2) * (-2 * it + 2) * (-2 * it + 2) * (-2 * it + 2) / 2 })
}